<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta name="theme-color" content="#000000" />
    <!--
      manifest.json provides metadata used when your web app is added to the
      homescreen on Android. See https://developers.google.com/web/fundamentals/engage-and-retain/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico" />
    <link href="https://fonts.googleapis.com/css2?family=Karla:wght@700&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Karla:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet"/>
    
<link href="https://fonts.googleapis.com/css2?family=Archivo&display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Alata&display=swap" rel="stylesheet"/>
<link rel="stylesheet" href="https://use.typekit.net/urw5jff.css"/>
<link href="https://fonts.googleapis.com/css2?family=Alata&display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Alata&family=Red+Rose&display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Alata&family=Kanit:wght@400&family=Red+Rose&display=swap" rel="stylesheet"/>
    <link
      rel="stylesheet"
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css"
      integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk"
      crossorigin="anonymous"
    />
    <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@700&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Satisfy&display=swap" rel="stylesheet"/>
    <link href='//fonts.googleapis.com/css?family=Lato:400,100,100italic,300,300italic,400italic,700,700italic,900,900italic' rel='stylesheet' type='text/css'/><link href='//fonts.googleapis.com/css?family=Raleway+Dots' rel='stylesheet' type='text/css'/>
    <link href="https://fonts.googleapis.com/css2?family=Merriweather+Sans&display=swap" rel="stylesheet"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Montserrat:400,800&display=swap"/>
    <link href="https://fonts.googleapis.com/css2?family=Bree+Serif&family=Sora:wght@600&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Galada&family=Merienda+One&family=Satisfy&display=swap" rel="stylesheet"/>
    <script
      src="https://kit.fontawesome.com/e5135d8a90.js"
      crossorigin="anonymous"
    ></script>
    <script src=http://cdn.webrupee.com/js type=”text/javascript”></script>
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Food Eazy</title>
  </head>

  <body>
    <!-- <script>
         window.addEventListener('beforeunload', function (e) { 
            e.preventDefault(); 
            e.returnValue = 'Are You Sure'; 
        })
//         const sign_in_btn = document.querySelector("#sign-in-btn");
// const sign_up_btn = document.querySelector("#sign-up-btn");
// const container = document.querySelector(".container1");

sign_up_btn.addEventListener("click", () => {
  container.classList.add("sign-up-mode");
});

sign_in_btn.addEventListener("click", () => {
  container.classList.remove("sign-up-mode");
});
       </script> -->
    <noscript>
      You need to enable JavaScript to run this app.
    </noscript>
    <div id="root"></div>
   
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
    
  </body>
</html>
