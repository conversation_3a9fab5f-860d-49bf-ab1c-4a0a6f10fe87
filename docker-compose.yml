version: "3"
services:
    mainapp:
        container_name: node
        build: ./
        ports:
             - "5000:5000"
    webserver:
        container_name: nginx
        image: nginx:latest
        ports:
             - "80:80"
             - "443:443"      
        volumes:
            - ./build:/var/www/html       
            - ./default.conf:/etc/nginx/conf.d/default.conf
            - certbot-etc:/etc/letsencrypt
            - certbot-var:/var/lib/letsencrypt   
    certbot:
      image: certbot/certbot
      container_name: certbot
      volumes:
        - certbot-etc:/etc/letsencrypt
        - certbot-var:/var/lib/letsencrypt
        - web-root:/var/www/html
      depends_on:
        - webserver
      command: certonly --webroot --webroot-path=/var/www/html --email <EMAIL> --agree-tos --no-eff-email --staging -d foodeazy.devforlife07.codes  -d www.foodeazy.devforlife07.codes
    # letsencrypt-nginx-container:
    #     container_name: 'letsencrypt-nginx-container'
    #     image: nginx:latest
    #     ports:
    #         - "80:80"
    #     volumes:
    #             - ./default.conf:/etc/nginx/conf.d/default.conf
    #             - ./build:/var/www/html
    #     depends_on:
    #         - mainapp        
    
            
       
networks:
  docker-network:
    driver: bridge

volumes:
  certbot-etc:
  certbot-var:  
  web-root:
      driver: local
      driver_opts:
        type: none
        device: C:\Users\<USER>\Desktop\foodeazy2
        o: bind   
