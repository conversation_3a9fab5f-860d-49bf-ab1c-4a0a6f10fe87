{"name": "react", "version": "1.0.0", "description": "React example starter project", "keywords": ["react", "starter"], "main": "src/index.js", "dependencies": {"@material-ui/core": "4.11.0", "@material-ui/icons": "4.9.1", "animate.css": "4.1.0", "axios": "0.19.2", "font-awesome": "4.7.0", "react": "16.12.0", "react-bootstrap": "1.2.2", "react-dom": "16.12.0", "react-router-dom": "5.2.0", "react-scripts": "^3.4.3", "react-templates": "0.6.3", "react-transition-group": "4.4.1"}, "devDependencies": {"typescript": "3.8.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "proxy": "http://localhost:5000"}