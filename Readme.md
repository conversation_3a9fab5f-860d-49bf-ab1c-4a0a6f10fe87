# Welcome to Food Eazy! ✨
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](https://foodeazy.herokuapp.com/)&nbsp;[![Build passing](https://img.shields.io/badge/Build-Passing-brightgreen.svg?style=flat-square)](https://foodeazy.herokuapp.com/)&nbsp;[![Open Source Love](https://badges.frapsoft.com/os/v1/open-source.svg?v=102)](https://foodeazy.herokuapp.com/)&nbsp;[![License](https://img.shields.io/badge/license-MIT-brightgreen)](https://foodeazy.herokuapp.com/)&nbsp;![Made with Love in India](https://madewithlove.org.in/badge.svg)

Food Eazy, is an online platform to order food and avoiding the hassle of going out or paying in cash. The users can opt for home delivery or take away as per thier choice and can pay thorugh cash or online methods (PayTM). In the times of COVID-19 where social distancing is such an important measure we think our website can help restaurants and customers.

**Project Link** - ***https://foodeazy.herokuapp.com/***
**Or**  - ***http://foodeazy.devforlife07.codes/***


## Features and Functionalities 😃
**User features**
 - User sign up and login
 - 2 factor authentication using email
 - Password Reset (Forget Password)
 - Search for food items
 - Sort the food items (By name or price)
 - Save Cart
 - Delete Cart
 - Order Type - Take Away or Delivery
 - Payment Methods - Cash or Online (using PayTM wallet, Debit/Credit card, Net Banking)
 - View your current and previous orders
 
 **Admin features** 
 
 - Add new dishes
 - Delete dishes
 ## Screenshots
 ### Login Page![enter image description here](https://raw.githubusercontent.com/Devforlife07/foodeazy/master/readme_images/login.png?token=ALT5AMAOLRKFR423UUHLK6C7KJJ4Y)
### Email Verify Page![enter image description here](https://raw.githubusercontent.com/mihir0699/foodeazy/master/readme_images/verify.png?token=ALT5AMCAUZLVKWU4UFAGZVC7KJKEG)
### Home Page
![enter image description here](https://raw.githubusercontent.com/mihir0699/foodeazy/master/readme_images/menu.png?token=ALT5AMAG4JWL7EOHY2IT7JC7KJKAQ)

### Cart
![enter image description here](https://raw.githubusercontent.com/mihir0699/foodeazy/master/readme_images/cart.png?token=ALT5AMCPL6C2W723CZYJZZK7KJKYY)
### Checkout
![enter image description here](https://raw.githubusercontent.com/mihir0699/foodeazy/master/readme_images/checkout.png?token=ALT5AMHX44VS5BX2PHTDSY27KJK4K)
### Contact Us
![enter image description here](https://raw.githubusercontent.com/Devforlife07/FoodEazy/master/readme_images/contact_us.png)
### Add Dish (Admin)
![enter image description here](https://raw.githubusercontent.com/mihir0699/foodeazy/master/readme_images/add_dish.png?token=ALT5AMAOEZ2YQ2ECJJMYZCC7KJK6I)
### Delete Dish (Admin)
![enter image description here](https://raw.githubusercontent.com/mihir0699/foodeazy/master/readme_images/delete_items.png?token=ALT5AMHLZNKOIMXWOKUKY7K7KJLCE)
## Tech Stack 💻

 - [React.js](https://reactjs.org/)
 - [Node.js](https://nodejs.org/en/)
 - [Express.js](https://expressjs.com/)
 - [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
 - [Material UI](https://material-ui.com/)
 - [React Bootstrap](https://react-bootstrap.github.io/)

## API :man_technologist:

 - [PayTM API](https://developer.paytm.com/docs/)
 - [Send Grid  API](https://sendgrid.com/)
 - [Cloudinary API](https://cloudinary.com/)

## Installation :zap:

 **1. Clone this repo by running the following command :-**
 ```bash
  git clone https://github.com/Devforlife07/FoodEazy
  cd foodeazy
 ```
 
 **2. Now install all the required packages by running the following commands :-**
 ```bash
  npm install 
  npm run install-client
 ```
 **3. Now start the react and node server together by running the following command :-**
 ```bash
  npm run dev
 ```
 **3. Create a `.env` file in the project root folder and copy the format of `.env.sample` file.**

   - `.env.sample` file contains all the environment variables required for running the project.
   
   
 **4.** **🎉  Open your browser and go to  `https://localhost:3000`**
 
## Contributors 🤝
 - [**Mihir Gupta**](https://github.com/mihir0699)  
 - [**Devansh Gera**](https://github.com/Devforlife07)
 
 
## 🤩 Don't forget to give this repo a ⭐ if you like this repo and want to appreciate our efforts
 

[![forthebadge](https://forthebadge.com/images/badges/built-with-love.svg)](https://forthebadge.com)
[![forthebadge](https://forthebadge.com/images/badges/built-by-developers.svg)](https://forthebadge.com)


